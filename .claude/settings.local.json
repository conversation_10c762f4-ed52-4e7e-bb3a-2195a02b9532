{"permissions": {"allow": ["WebFetch(domain:juicefs.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(./deploy.sh)", "Bash(docker service logs:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(docker container exec:*)", "Bash(ss:*)", "Bash(nmap:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker service ps:*)", "<PERSON><PERSON>(sudo netstat:*)", "Bash(docker stack:*)", "Bash(docker service:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(python:*)", "mcp__zen__planner", "mcp__zen__chat", "WebFetch(domain:docs.docker.com)", "<PERSON><PERSON>(git mv:*)", "mcp__zen__analyze", "Ba<PERSON>(dig:*)", "<PERSON><PERSON>(timeout 5 curl -I https://traefik.skinbase.io)", "<PERSON><PERSON>(timeout:*)", "mcp__zen__thinkdeep", "Bash(gh api:*)", "mcp__zen__codereview", "mcp__zen__challenge", "WebFetch(domain:github.com)", "WebFetch(domain:scrapoxy.io)"], "deny": []}}