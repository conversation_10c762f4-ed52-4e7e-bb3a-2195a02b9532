name: Container Image Cleanup

on:
  # Run daily at 3:00 AM UTC (4:00 AM CEST)
  schedule:
    - cron: '0 3 * * *'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      keep_versions:
        description: 'Number of recent versions to keep'
        required: false
        default: '5'
        type: string

jobs:
  cleanup:
    runs-on: self-hosted
    permissions:
      packages: write
      contents: read
    strategy:
      matrix:
        package: [external-api, web-scraping-worker, internal-api, web]
    
    steps:
      - name: Cleanup old versions of ${{ matrix.package }}
        uses: actions/delete-package-versions@v5
        with:
          package-name: ${{ matrix.package }}
          package-type: container
          owner: skinbase-io
          min-versions-to-keep: ${{ github.event.inputs.keep_versions || '5' }}
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Report cleanup results for ${{ matrix.package }}
        if: always()
        run: |
          echo "Container image cleanup completed for ${{ matrix.package }}"
          echo "Versions kept: ${{ github.event.inputs.keep_versions || '5' }}"