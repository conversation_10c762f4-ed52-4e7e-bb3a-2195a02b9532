rm -rf ../../prod-venv
python3 -m venv ../../prod-venv
source ../../prod-venv/bin/activate

python3 -m pip install ansible==11.7.0
python3 -m pip install yq
python3 -m pip install passlib
python3 -m pip install zeep
python3 -m pip install oci
python3 -m pip install https://github.com/neuroforgede/docker-stack-deploy/archive/refs/tags/0.2.12.zip
ansible-galaxy collection install artis3n.tailscale
ansible-galaxy collection install git+https://github.com/TLii/ansible-netcup.git
ansible-galaxy collection install oracle.oci
ansible-galaxy collection install hetzner.hcloud
curl -o inventory/ansible_tailscale_inventory.py https://raw.githubusercontent.com/m4wh6k/ansible-tailscale-inventory/main/ansible_tailscale_inventory.py
#chmod +x inventory/ansible_tailscale_inventory.py
