# CAREFUL: insecure
ansible_ssh_common_args: "-o StrictHostKeyChecking=no"

project_base_dir: "{{ CWD }}"
ansible_initial_user: "root"
ansible_initial_ssh_private_key_file: "{{ CWD }}/ssh_keys/root_rsa.decrypted"

ansible_ssh_private_key_file: "{{ CWD }}/ssh_keys/ansible_rsa.decrypted"
global_ansible_ssh_private_key_file: "{{ CWD }}/ssh_keys/ansible_rsa.decrypted"

docker_pinned_package: "*"
containerd_pinned_package: "*"

docker_registry_client_credentials: []
#- user: ""
#  passwd: ""
#  registry: ""

# for redis you might want to set this:
docker_sysctl_settings:
  - key: vm.overcommit_memory
    value: "1"

docker_swarm_main_manager: "netcup-manager-1"
docker_swarm_ingress_subnet: "10.0.0.0/16"
docker_swarm_ingress_network_name: "ingress"

# we have things in a private network anyways
docker_swarm_ingress_network_encrypt: True
docker_swarm_traefik_ingress_network_encrypt: True

docker_swarm_default_ip_addr_pool: "10.0.0.0/8"
docker_swarm_default_ip_addr_pool_mask_length: "24"

ssh_developer_accounts:
  - name: "someuser"
    groups:
      - "docker"
