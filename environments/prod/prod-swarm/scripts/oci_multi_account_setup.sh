#!/bin/bash

# OCI Multi-Account Setup Script
# This script helps manage multiple OCI accounts for the skinbase-ops project

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
INVENTORY_DIR="$PROJECT_DIR/inventory"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v ansible &> /dev/null; then
        print_error "Ansible is not installed. Please install it first."
        exit 1
    fi
    
    if ! ansible-galaxy collection list | grep -q "oracle.oci"; then
        print_error "Oracle OCI Ansible collection is not installed."
        print_info "Run: ansible-galaxy collection install oracle.oci"
        exit 1
    fi
    
    if ! python3 -c "import oci" &> /dev/null; then
        print_error "OCI Python SDK is not installed."
        print_info "Run: pip install oci"
        exit 1
    fi
    
    print_info "All prerequisites are met."
}

# Function to setup OCI config profiles
setup_oci_config() {
    local config_file="$HOME/.oci/config"
    
    print_info "Setting up OCI configuration..."
    
    if [[ ! -f "$config_file" ]]; then
        print_error "OCI config file not found at $config_file"
        print_info "Please run 'oci setup config' first to create the initial configuration."
        exit 1
    fi
    
    print_info "Current OCI config file exists at: $config_file"
    print_info "To add additional accounts, edit this file and add new profiles like:"
    echo ""
    echo "[ACCOUNT2]"
    echo "user=ocid1.user.oc1..your_user_ocid"
    echo "fingerprint=xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx"
    echo "key_file=/path/to/your/private/key.pem"
    echo "tenancy=ocid1.tenancy.oc1..your_tenancy_ocid"
    echo "region=eu-frankfurt-1"
    echo ""
}

# Function to test OCI connectivity
test_oci_connectivity() {
    local profile="${1:-DEFAULT}"
    
    print_info "Testing OCI connectivity for profile: $profile"
    
    # Test using Python OCI SDK
    python3 -c "
import oci
try:
    config = oci.config.from_file(profile_name='$profile')
    identity = oci.identity.IdentityClient(config)
    user = identity.get_user(config['user'])
    print(f'Successfully connected to OCI as: {user.data.name}')
    print(f'Tenancy: {user.data.tenancy_id}')
    print(f'Region: {config[\"region\"]}')
except Exception as e:
    print(f'Error connecting to OCI: {e}')
    exit(1)
"
}

# Function to test inventory
test_inventory() {
    local inventory_file="$1"
    
    print_info "Testing inventory file: $inventory_file"
    
    if [[ ! -f "$inventory_file" ]]; then
        print_error "Inventory file not found: $inventory_file"
        exit 1
    fi
    
    # Test ansible inventory
    if ansible-inventory -i "$inventory_file" --list > /dev/null 2>&1; then
        print_info "Inventory file is valid."
        
        # Show discovered hosts
        print_info "Discovered hosts:"
        ansible-inventory -i "$inventory_file" --list | jq -r '.["_meta"]["hostvars"] | keys[]' 2>/dev/null || echo "No hosts found"
    else
        print_error "Inventory file is invalid or failed to load."
        exit 1
    fi
}

# Function to setup environment variables
setup_env_vars() {
    print_info "Environment variables setup:"
    print_info "To use environment variables for OCI authentication, set these:"
    echo ""
    echo "export OCI_USER_OCID=\"ocid1.user.oc1..your_user_ocid\""
    echo "export OCI_TENANCY_OCID=\"ocid1.tenancy.oc1..your_tenancy_ocid\""
    echo "export OCI_REGION=\"eu-frankfurt-1\""
    echo "export OCI_KEY_FILE=\"/path/to/your/private/key.pem\""
    echo "export OCI_FINGERPRINT=\"xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx\""
    echo ""
    print_info "Then use the oracle.oci.envvars.yml inventory file."
}

# Function to list available inventory files
list_inventory_files() {
    print_info "Available OCI inventory files:"
    
    for file in "$INVENTORY_DIR"/oracle.oci.*.yml; do
        if [[ -f "$file" ]]; then
            basename "$file"
        fi
    done
}

# Function to run ansible with specific inventory
run_ansible() {
    local inventory_file="$1"
    local playbook="${2:-}"
    
    if [[ -z "$playbook" ]]; then
        print_error "Playbook not specified"
        exit 1
    fi
    
    print_info "Running ansible with inventory: $inventory_file"
    ansible-playbook -i "$inventory_file" "$playbook"
}

# Main function
main() {
    case "${1:-help}" in
        "check")
            check_prerequisites
            ;;
        "setup-config")
            setup_oci_config
            ;;
        "test-profile")
            test_oci_connectivity "${2:-DEFAULT}"
            ;;
        "test-inventory")
            test_inventory "${2:-$INVENTORY_DIR/oracle.oci.yml}"
            ;;
        "setup-env")
            setup_env_vars
            ;;
        "list")
            list_inventory_files
            ;;
        "run")
            run_ansible "${2:-$INVENTORY_DIR/oracle.oci.yml}" "${3:-}"
            ;;
        "help"|*)
            echo "OCI Multi-Account Setup Script"
            echo ""
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  check                    - Check prerequisites"
            echo "  setup-config             - Setup OCI configuration"
            echo "  test-profile [profile]   - Test OCI connectivity for a profile"
            echo "  test-inventory [file]    - Test inventory file"
            echo "  setup-env                - Show environment variables setup"
            echo "  list                     - List available inventory files"
            echo "  run <inventory> <playbook> - Run ansible with specific inventory"
            echo "  help                     - Show this help"
            echo ""
            echo "Examples:"
            echo "  $0 check"
            echo "  $0 test-profile DEFAULT"
            echo "  $0 test-inventory inventory/oracle.oci.account1.yml"
            echo "  $0 run inventory/oracle.oci.account1.yml playbook.yml"
            ;;
    esac
}

# Run main function with all arguments
main "$@"