#!/bin/bash

# Phase 1: Basic Setup and Tailscale
# 
# Usage:
#   ./setup-phase1.sh                    # Run on all servers (including existing ones)
#   ./setup-phase1.sh hostname           # Run only on specific server (recommended for new nodes)
#   ./setup-phase1.sh "host1,host2"      # Run on multiple specific servers
#
# Note: When adding new nodes to existing cluster, use the hostname parameter to avoid
# SSH timeouts on existing nodes that have port 22 blocked via UFW for public IPs.

check_result() {
    ___RESULT=$?
    if [ $___RESULT -ne 0 ]; then
        echo "$1"
        exit 1
    fi
}

echo "====================================="
echo "Phase 1: Basic Setup and Tailscale"
echo "====================================="

source ../../prod-venv/bin/activate

set -a
export HCLOUD_TOKEN=$(yq -r .hcloud_token secrets/hcloud_token.decrypted.yml)
export NETCUP_CUSTOMER_ID=$(yq -r .customer_id secrets/netcup.decrypted.yml)
export NETCUP_WS_PASSWORD=$(yq -r .password secrets/netcup.decrypted.yml)

EXTRA_VARS="{ \"swarmsible_vars_path\": \"$(realpath vars)\", \"CWD\": \"$(pwd)\" }"

# Handle optional --limit parameter for specific hosts
LIMIT_HOSTS=""
if [ "$1" != "" ]; then
    LIMIT_HOSTS="--limit=$1"
    echo "Running on specific hosts: $1"
fi

# Run phase 1 - basic setup with Tailscale
ansible-playbook -i inventory/hcloud.yml -i inventory/netcup.yml -i inventory/oracle.oci.yml \
  ./swarmsible/ansible_setup.yml --extra-vars="$EXTRA_VARS" $LIMIT_HOSTS
#ansible-inventory -i inventory/hcloud.yml -i inventory/netcup.yml -i inventory/oracle.oci.yml --graph
#ansible-inventory -i inventory/netcup.yml --list
check_result "Failed to run phase 1 (ansible_setup)"

echo ""
echo "====================================="
echo "Phase 1 Complete!"
echo "====================================="
echo ""
echo "Next steps:"
echo "1. Go to https://login.tailscale.com/admin/machines"
echo "2. Add tags to your machines:"
echo "   - Manager node(s): tag:swarm_manager"
echo "   - Worker node(s): tag:swarm_worker"
echo "3. Once tags are configured, run: ./setup-phase2.sh"
echo ""
echo "To verify tags, run: ./verify-tags.sh"