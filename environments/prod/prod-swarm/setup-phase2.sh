#!/bin/bash

check_result() {
    ___RESULT=$?
    if [ $___RESULT -ne 0 ]; then
        echo "$1"
        exit 1
    fi
}

echo "====================================="
echo "Phase 2: Docker Swarm Configuration"
echo "====================================="

# Check if virtual environment exists and activate it
if [ -f "../../prod-venv/bin/activate" ]; then
    source ../../prod-venv/bin/activate
fi

set -a
export HCLOUD_TOKEN=$(yq -r .hcloud_token secrets/hcloud_token.decrypted.yml)
export NETCUP_CUSTOMER_ID=$(yq -r .customer_id secrets/netcup.decrypted.yml)
export NETCUP_WS_PASSWORD=$(yq -r .password secrets/netcup.decrypted.yml)

EXTRA_VARS="{ \"swarmsible_vars_path\": \"$(realpath vars)\", \"CWD\": \"$(pwd)\" }"

# Check if tags are configured
echo ""
echo "Checking Tailscale tags configuration..."
echo ""

# Show current Docker Swarm groups (with UTF-8 handling)
export PYTHONIOENCODING=utf-8
ansible-inventory -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py --graph 2>/dev/null | grep -A 20 -E "@docker_swarm:|docker_swarm_manager|docker_swarm_worker" || true

echo ""
echo "====================================="

# Check if we have at least one manager (with UTF-8 handling and error checking)
INVENTORY_JSON=$(ansible-inventory -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py --list 2>/dev/null || echo '{}')

# Get manager count with fallback to 0 - check both direct hosts and child groups
MANAGER_HOSTS=$(echo "$INVENTORY_JSON" | jq -r '.docker_swarm_manager.hosts // []' 2>/dev/null)
MANAGER_CHILD_HOSTS=$(echo "$INVENTORY_JSON" | jq -r '[.docker_swarm_manager.children[]? as $child | .[$child].hosts[]?] // []' 2>/dev/null)
MANAGER_COUNT=$(echo "$MANAGER_HOSTS $MANAGER_CHILD_HOSTS" | jq -s 'add | length' 2>/dev/null || echo "0")

WORKER_HOSTS=$(echo "$INVENTORY_JSON" | jq -r '.docker_swarm_worker.hosts // []' 2>/dev/null)
WORKER_CHILD_HOSTS=$(echo "$INVENTORY_JSON" | jq -r '[.docker_swarm_worker.children[]? as $child | .[$child].hosts[]?] // []' 2>/dev/null)
WORKER_COUNT=$(echo "$WORKER_HOSTS $WORKER_CHILD_HOSTS" | jq -s 'add | length' 2>/dev/null || echo "0")

# Ensure we have numeric values
MANAGER_COUNT=${MANAGER_COUNT:-0}
WORKER_COUNT=${WORKER_COUNT:-0}

if [ "$MANAGER_COUNT" -eq 0 ] || [ -z "$MANAGER_COUNT" ]; then
    echo "ERROR: No manager nodes found!"
    echo "Please add 'swarm_manager' tag to at least one machine in Tailscale Admin"
    exit 1
fi

echo "Found $MANAGER_COUNT manager node(s) and $WORKER_COUNT worker node(s)"
echo ""

read -p "Are the tags configured correctly? Continue? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Please configure tags first:"
    echo "1. Go to https://login.tailscale.com/admin/machines"
    echo "2. Add appropriate tags (swarm_manager/swarm_worker)"
    echo "3. Run this script again"
    exit 1
fi

echo ""
echo "Running Docker Swarm configuration..."

# Run phase 2 - Docker Swarm setup
ansible-playbook -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
  ./swarmsible/docker_swarm.yml --extra-vars="$EXTRA_VARS"
check_result "Failed to run phase 2 (docker_swarm)"

echo ""
echo "====================================="
echo "Phase 2 Complete!"
echo "====================================="
echo ""
echo "Docker Swarm cluster has been configured."
echo ""
echo "To configure developer accounts, run: ./setup-phase3.sh"
echo ""
echo "To deploy stacks, use scripts in the stacks/ directory"