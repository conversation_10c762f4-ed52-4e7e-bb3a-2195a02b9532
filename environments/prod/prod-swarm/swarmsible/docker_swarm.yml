---
- hosts: docker_swarm
  become: true
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"
  pre_tasks:
    - name: Get Tailscale IPv4 for this host
      ansible.builtin.command: tailscale ip -4
      register: tailscale_ip_result
      changed_when: false
    - name: Set tailscale_ip fact
      ansible.builtin.set_fact:
        my_tailscale_ip: "{{ tailscale_ip_result.stdout }}"
  roles:
    # copy the ssl certs from the beginning so all nodes have the certs on the system
    # so all mounts work
    - copy-ssl-certs
    - docker-sysctl-tune
    - docker-setup
    - docker-pin-versions
    - docker-post-setup
    - docker-login
    - ufw-docker-install
    - docker-swarm-hosts

- hosts: docker_swarm_manager
  become: true
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"
  roles:
    - docker-swarm-init
    - docker-swarm-add-manager

- hosts: docker_swarm_worker
  become: true
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"
  roles:
    - docker-swarm-add-worker

- hosts: docker_swarm
  become: true
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"
  pre_tasks:
    - name: Get Tailscale IPv4 for this host
      ansible.builtin.command: tailscale ip -4
      register: tailscale_ip_result
      changed_when: false
    - name: Set tailscale_ip fact
      ansible.builtin.set_fact:
        my_tailscale_ip: "{{ tailscale_ip_result.stdout }}"
  roles:
    - copy-ssl-certs
    - docker-sysctl-tune
    - docker-swarm-labels
