---
# Firewall-only configuration playbook
# This playbook configures UFW firewall rules without any other setup
#
# Usage:
#   ansible-playbook -i inventory/hcloud.yml -i inventory/netcup.yml -i inventory/oracle.oci.yml \
#     ./swarmsible/firewall_setup.yml --extra-vars="$EXTRA_VARS"
#
# Prerequisites:
# - Servers must be accessible via SSH
# - UFW package should be installed (if not, it will be installed)
# - Tailscale should be configured if you want Tailscale-specific rules

- hosts: all
  become: true
  vars:
      ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
      ansible_user: "{{ global_ansible_user | default('ansible') }}"

  pre_tasks:
      - name: 'Ensure UFW is installed'
        ansible.builtin.package:
            name: ufw
            state: present
            update_cache: true

      - name: 'Check if Tailscale is available'
        ansible.builtin.command: which tailscale
        register: tailscale_check
        failed_when: false
        changed_when: false

      - name: 'Get Tailscale IPv4 if available'
        ansible.builtin.command: tailscale ip -4
        register: tailscale_ip_result
        failed_when: false
        changed_when: false
        when: tailscale_check.rc == 0

      - name: 'Display Tailscale status'
        ansible.builtin.debug:
            msg: "{{ 'Tailscale available with IP: ' + tailscale_ip_result.stdout if tailscale_check.rc == 0 and tailscale_ip_result.stdout != '' else 'Tailscale not available or not configured' }}"

  roles:
      - role: firewall

  post_tasks:
      - name: 'Display UFW status'
        ansible.builtin.command: ufw status verbose
        register: ufw_status
        changed_when: false

      - name: 'Show current firewall configuration'
        ansible.builtin.debug:
            var: ufw_status.stdout_lines
