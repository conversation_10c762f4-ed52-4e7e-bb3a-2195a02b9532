# - name: "<PERSON><PERSON>lar<PERSON> | Force immediate reload of UFW after cleanup"
#   become: true
#   ansible.builtin.command: "ufw reload"
#   changed_when: true
# ------------------------------------------------------------------------------------

- name: Cloudflare | Fetch IPv4 address list
  ansible.builtin.uri:
      url: https://www.cloudflare.com/ips-v4
      return_content: yes
  register: cloudflare_ipv4_ranges

- name: Cloudflare | Fetch IPv6 address list
  ansible.builtin.uri:
      url: https://www.cloudflare.com/ips-v6
      return_content: yes
  register: cloudflare_ipv6_ranges

- name: Cloudflare | Create combined IP address list
  ansible.builtin.set_fact:
      cloudflare_ips: '{{ cloudflare_ipv4_ranges.content.splitlines() + cloudflare_ipv6_ranges.content.splitlines() }}'

- name: <PERSON>flare | Separate IPs into IPv4 and IPv6 lists
  ansible.builtin.set_fact:
      cloudflare_ipv4s: "{{ cloudflare_ips | select('contains', '.') | list }}"
      cloudflare_ipv6s: "{{ cloudflare_ips | select('contains', ':') | list }}"

- name: Cloudflare | Manage IPv4 rules in /etc/ufw/user.rules
  become: true
  ansible.builtin.blockinfile:
      path: /etc/ufw/user.rules
      insertbefore: '^### END RULES ###'
      marker: '# {mark} ANSIBLE MANAGED BLOCK - Cloudflare IPs'
      block: |
          ### RULES ###
          # Rules for Cloudflare IPv4
          {% for ip in cloudflare_ipv4s %}
          ### tuple ### allow tcp 80 0.0.0.0/0 any {{ ip }}
          -A ufw-user-input -p tcp --dport 80 -s {{ ip }} -j ACCEPT
          ### tuple ### allow tcp 443 0.0.0.0/0 any {{ ip }}
          -A ufw-user-input -p tcp --dport 443 -s {{ ip }} -j ACCEPT
          {% endfor %}
  notify: Reload UFW
  when: cloudflare_ipv4s is defined and cloudflare_ipv4s | length > 0

- name: Cloudflare | Manage IPv6 rules in /etc/ufw/user6.rules
  become: true
  ansible.builtin.blockinfile:
      path: /etc/ufw/user6.rules
      insertbefore: '^### END RULES ###'
      marker: '# {mark} ANSIBLE MANAGED BLOCK - Cloudflare IPs'
      block: |
          ### RULES ###
          # Rules for Cloudflare IPv6
          {% for ip in cloudflare_ipv6s %}
          ### tuple ### allow tcp 80 ::/0 any {{ ip }}
          -A ufw6-user-input -p tcp --dport 80 -s {{ ip }} -j ACCEPT
          ### tuple ### allow tcp 443 ::/0 any {{ ip }}
          -A ufw6-user-input -p tcp --dport 443 -s {{ ip }} -j ACCEPT
          {% endfor %}
  notify: Reload UFW
  when: cloudflare_ipv6s is defined and cloudflare_ipv6s | length > 0
