---

- name: "ensure /data/ansible exists"
  file:
    path: /data/ansible
    state: directory

- name: "ensure /data/ansible/state exists"
  file:
    path: /data/ansible/state
    state: directory

- name: "ensure /data/ansible/state/docker exists"
  file:
    path: /data/ansible/state/docker
    state: directory

- name: "ensure /data/ansible/state/docker/changes exists"
  file:
    path: /data/ansible/state/docker/changes
    state: directory

- name: Install Docker SDK for Python (apt)
  ansible.builtin.apt:
    name: python3-docker
    state: present
    update_cache: yes

- include_tasks: subtasks/2019_03_15.yml
- include_tasks: subtasks/2020_07_17.yml
- include_tasks: subtasks/2021_03_04.yml
- include_tasks: subtasks/2022_06_11.yml