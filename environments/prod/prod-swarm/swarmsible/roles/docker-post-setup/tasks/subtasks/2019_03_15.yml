---
- name: check if docker_changes_2019_03_15 were already applied
  stat:
    path: "/data/ansible/state/docker/changes/2019_03_15"
  register: "docker_changes_2019_03_15"

- name: set docker_changes_2019_03_15_val
  set_fact:
    docker_changes_2019_03_15_val: "{{ docker_changes_2019_03_15.stat.exists }}"

- name: set up docker changes for 2019_03_15
  when: not docker_changes_2019_03_15_val|bool
  block:

    - name: "copy daemon.json to /etc/docker/daemon.json"
      copy:
        src: "2019_03_15/daemon.json"
        dest: "/etc/docker/daemon.json"
        mode: 0600
        owner: root
        group: root

    - name: "change docker config file to /etc/docker/daemon.json"
      command: echo 'DOCKER_OPTS="--config-file=/etc/docker/daemon.json"' > /etc/default/docker

    - name: "restart docker"
      service:
        name: docker
        state: restarted
        enabled: yes

- name: "touch /data/ansible/state/docker/changes/2019_03_15"
  file:
    path: "/data/ansible/state/docker/changes/2019_03_15"
    state: touch
    mode: "u=rw,g=r,o=r"