---
- name: check if docker_changes_2020_07_17 were already applied
  stat:
    path: "/data/ansible/state/docker/changes/2020_07_17"
  register: "docker_changes_2020_07_17"

- name: set docker_changes_2020_07_17_val
  set_fact:
    docker_changes_2020_07_17_val: "{{ docker_changes_2020_07_17.stat.exists }}"

- name: set up docker changes for 2020_07_17
  when: not docker_changes_2020_07_17_val|bool
  block:

    - name: "copy daemon.json to /etc/docker/daemon.json"
      copy:
        src: "2020_07_17/daemon.json"
        dest: "/etc/docker/daemon.json"
        mode: 0600
        owner: root
        group: root

    - name: "change docker config file to /etc/docker/daemon.json"
      command: echo 'DOCKER_OPTS="--config-file=/etc/docker/daemon.json"' > /etc/default/docker

    - name: "restart docker"
      service:
        name: docker
        state: restarted
        enabled: yes

- name: "touch /data/ansible/state/docker/changes/2020_07_17"
  file:
    path: "/data/ansible/state/docker/changes/2020_07_17"
    state: touch
    mode: "u=rw,g=r,o=r"