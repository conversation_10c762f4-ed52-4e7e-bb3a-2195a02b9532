---

- include_tasks: setup-Debian.yml
  when: ansible_os_family == 'Debian'

- name: check if package is installed
  package:
    name: "{{ docker_packages[0] }}"
    state: present
  # important: essentially a 'dry-run'
  check_mode: true
  register: docker_installed

- name: Install Docker packages (with downgrade option).
  package:
    name: "{{ docker_packages }}"
    state: "present"
  when: docker_installed.changed

- name: Ensure /etc/docker/ directory exists.
  file:
    path: /etc/docker
    state: directory
    mode: 0755

- name: Ensure Docker is started and enabled at boot.
  service:
    name: docker
    state: "started"
    enabled: true
