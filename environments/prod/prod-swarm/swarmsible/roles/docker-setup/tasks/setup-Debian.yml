- name: Ensure old versions of <PERSON><PERSON> are not installed.
  package:
    name:
      - docker
      - docker-engine
    state: absent

- name: Ensure dependencies are installed.
  apt:
    name:
      - apt-transport-https
      - ca-certificates
    state: present

- name: Ensure additional dependencies are installed (on Ubuntu < 20.04 and any other systems).
  apt:
    name: gnupg2
    state: present
  when: ansible_distribution != 'Ubuntu' or ansible_distribution_version is version('20.04', '<')

- name: Ensure additional dependencies are installed (on Ubuntu >= 20.04).
  apt:
    name: gnupg
    state: present
  when: ansible_distribution == 'Ubuntu' and ansible_distribution_version is version('20.04', '>=')

- name: Add Docker apt key.
  ansible.builtin.get_url:
    url: "{{ docker_apt_gpg_key }}"
    dest: /etc/apt/trusted.gpg.d/docker.asc
    mode: '0644'
    force: true
  register: add_repository_key
  ignore_errors: "{{ docker_apt_ignore_key_error }}"

- name: Ensure curl is present (on older systems without SNI).
  package: name=curl state=present
  when: add_repository_key is failed

- name: Add Docker apt key (alternative for older systems without SNI).
  shell: >
    curl -sSL {{ docker_apt_gpg_key }} | apt-key add -
  args:
    warn: false
  when: add_repository_key is failed

- name: Add Docker repository.
  apt_repository:
    repo: "{{ docker_apt_repository }}"
    state: present
    update_cache: true