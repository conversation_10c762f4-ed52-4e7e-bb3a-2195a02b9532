---
- name: Check if Swarm is Already Initialized
  shell: docker node ls
  register: swarm_status
  ignore_errors: true

- name: Add Managers to the Swarm
  shell: |
    docker swarm join \
      --token {{ hostvars[docker_swarm_main_manager]['manager_token']['stdout'] }} \
      --advertise-addr={{ my_tailscale_ip }} \
      {{ hostvars[docker_swarm_main_manager]['my_tailscale_ip'] }}
  when: swarm_status.rc != 0