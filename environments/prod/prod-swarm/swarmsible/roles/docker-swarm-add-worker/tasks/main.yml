---
- name: Check if Swarm is Already Initialized
  shell: docker info --format '{{ '{{' }}.Swarm.LocalNodeState{{ '}}' }}'
  register: swarm_status
  ignore_errors: true

- name: Add Workers to the Swarm
  shell: |
    docker swarm join \
      --token {{ hostvars[docker_swarm_main_manager]['worker_token']['stdout'] }} \
      --advertise-addr={{ my_tailscale_ip }} \
      {{ hostvars[docker_swarm_main_manager]['my_tailscale_ip'] }}
  when: swarm_status.rc != 0 or (swarm_status.stdout | default('not_found')) != 'active'
