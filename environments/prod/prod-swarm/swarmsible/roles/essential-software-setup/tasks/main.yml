---
# careful: this is executed on the management node as well, so this should
# really only include _essential_ software

- name: Install essential packages
  apt:
    name: "{{ packages }}"
    update_cache: "{{ apt_update_cache | default('True') }}"
  vars:
    packages:
    - vim
    - ufw
    - sudo
    - python3-setuptools
    - python3-pip
    - python3-passlib
    - git
    - acl

- name: molly guard
  include_tasks: subtasks/molly-guard.yml
