---
- name: 'Reset UFW rules'
  community.general.ufw:
    state: reset
  notify: Reload UFW

- name: 'Set UFW default policies and enable UFW'
  community.general.ufw:
    state: enabled
    policy: '{{ item.policy }}'
    direction: '{{ item.direction }}'
  with_items:
    - { direction: 'incoming', policy: 'deny' }
    - { direction: 'outgoing', policy: 'allow' }
  notify: Reload UFW

- name: 'Allow all incoming access on tailscale0 interface'
  community.general.ufw:
    rule: allow
    interface: tailscale0
    direction: in
    comment: 'Allow all incoming from Tailscale private network'
  notify: Reload UFW

- name: 'Apply Cloudflare firewall rules'
  ansible.builtin.import_role:
    name: cloudflare-firewall
