---
- name: Configure netplan for Netcup servers only
  block:
    - name: Check if eth0 (main interface) is active before proceeding
      shell: ip link show eth0 | grep -q "state UP"
      register: eth0_check
      changed_when: false
      failed_when: eth0_check.rc != 0

    - name: Ensure SSH connectivity is stable
      wait_for_connection:
        timeout: 10

    - name: Check if eth1 interface exists
      shell: ip link show eth1
      register: eth1_check
      changed_when: false
      failed_when: false

    - name: Skip netplan configuration if eth1 doesn't exist
      debug:
        msg: "eth1 interface not found. Skipping netplan configuration."
      when: eth1_check.rc != 0

    - name: Continue with eth1 configuration
      block:
        - name: Calculate deterministic sequential IP assignment
          set_fact:
            sorted_netcup_servers: "{{ groups['netcup_servers'] | sort }}"

        - name: Assign sequential IP based on sorted hostname position
          set_fact:
            netcup_auto_ip: "{{ netcup_base_ip }}.{{ netcup_ip_start + sorted_netcup_servers.index(inventory_hostname) }}"

        - name: Debug assigned IP
          debug:
            msg: "Assigned IP {{ netcup_auto_ip }} to server {{ inventory_hostname }} (sequential assignment, position {{ sorted_netcup_servers.index(inventory_hostname) }})"

        - name: Create netplan configuration for eth1 interface
          template:
            src: netplan-eth1.yaml.j2
            dest: "{{ netcup_netplan_config_path }}"
            owner: root
            group: root
            mode: '0600'
            backup: yes
          notify: Apply netplan configuration

        - name: Bring up eth1 interface first
          command: ip link set eth1 up
          register: eth1_up
          changed_when: false
          failed_when: false

        - name: Apply netplan configuration directly (skip problematic try)
          command: netplan apply
          register: netplan_apply
          changed_when: false
          failed_when: netplan_apply.rc != 0

        - name: Verify netplan was applied successfully
          debug:
            msg: "Netplan configuration applied successfully"
          when: netplan_apply.rc == 0

        - name: Wait for other servers to configure their interfaces (avoid race conditions)
          pause:
            seconds: "{{ (groups['netcup_servers'] | sort).index(inventory_hostname) * 3 }}"
          when: groups['netcup_servers'] | length > 1

        - name: Verify eth1 interface configuration
          shell: ip addr show eth1 | grep "inet {{ netcup_auto_ip }}"
          register: interface_check
          changed_when: false
          failed_when: interface_check.rc != 0
          retries: 3
          delay: 2

        - name: Test connectivity to other Netcup servers (if multiple servers)
          shell: |
            if [ "{{ groups['netcup_servers'] | length }}" -gt 1 ]; then
              {% for host in (groups['netcup_servers'] | sort) %}
              {% if host != inventory_hostname %}
              other_ip="{{ netcup_base_ip }}.{{ netcup_ip_start + (groups['netcup_servers'] | sort).index(host) }}"
              echo "Testing connectivity to {{ host }} at $other_ip"
              ping -c 1 -W 5 $other_ip || echo "Failed to ping {{ host }} at $other_ip"
              {% endif %}
              {% endfor %}
            else
              echo "Only one Netcup server configured, skipping connectivity test"
            fi
          register: connectivity_check
          changed_when: false
          failed_when: false

        - name: Display connectivity test results
          debug:
            msg: "{{ connectivity_check.stdout_lines }}"
          when: connectivity_check is defined

      when: eth1_check.rc == 0  # Only run this block if eth1 exists

  when: "'netcup_servers' in group_names"