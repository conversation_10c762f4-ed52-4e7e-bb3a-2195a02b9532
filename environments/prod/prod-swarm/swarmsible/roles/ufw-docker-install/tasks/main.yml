- name: Copy ufw-docker from skinbase repo
  copy:
    src: ufw-docker
    dest: /usr/local/bin/ufw-docker

- name: ensure permissions on ufw-docker to 751
  file:
    path: /usr/local/bin/ufw-docker
    owner: root
    group: root
    mode: '0751'

- name: Get Tailscale subnet (standard CGNAT range)
  set_fact:
    tailscale_subnet: "100.64.0.0/10"

- name: run ufw-docker install with Tailscale subnet
  args:
    executable: /bin/bash
  shell: |
      check_result () {
        ___RESULT=$?
        if [ $___RESULT -ne 0 ]; then
            echo $1
            exit 1
        fi
      }
      ufw-docker install --docker-subnets {{ tailscale_subnet }}
      check_result "failed to run 'ufw-docker install'"
  register: ufw_docker_result
  changed_when: "'Backing up' in ufw_docker_result.stderr"

- name: restart ufw
  service:
    name: ufw
    state: restarted


