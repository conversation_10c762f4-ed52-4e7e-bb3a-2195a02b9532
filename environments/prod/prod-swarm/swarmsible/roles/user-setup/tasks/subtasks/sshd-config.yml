---
# Note: with_first_found searches in the current dir!
# TODO: should we change the path for these then?

- debug:
    var: ansible_distribution

- name: gather os specific variables
  include_vars: '{{ item }}'
  with_first_found:
    - '{{ ansible_distribution }}-{{ ansible_distribution_major_version}}.yml'
    - '{{ ansible_distribution }}.yml'
    - 'defaults.yml'

- name: configure ssh
  template:
    src: '{{ item }}'
    dest: '{{ SSH_CONFIG }}'
    backup: yes
  with_first_found:
    - '{{ ansible_distribution }}-{{ ansible_distribution_major_version }}.sshd_config.j2'
    - '{{ ansible_distribution }}.sshd_config.j2'

- name: disable ssh socket activation for better security
  systemd:
    name: ssh.socket
    enabled: no
    state: stopped
  ignore_errors: yes

- name: reload ssh
  systemd:
    name: ssh
    state: reloaded
