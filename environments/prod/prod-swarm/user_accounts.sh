#!/bin/bash

check_result () {
    ___RESULT=$?
    if [ $___RESULT -ne 0 ]; then
        echo $1
        exit 1
    fi
}

source ../../prod-venv/bin/activate

EXTRA_VARS="{ \"swarmsible_vars_path\": \"$(realpath vars)\", \"CWD\": \"$(pwd)\" }"

ansible-playbook -i inventory/hosts.ini ./swarmsible/swarmsible/swarmsible/developer_accounts.yml --extra-vars="$EXTRA_VARS"
check_result "failed to run developer_accounts"