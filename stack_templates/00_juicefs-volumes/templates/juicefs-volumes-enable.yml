services:
  docker_volume_juicefs:
    image: docker
    command: sh -c "(docker plugin inspect juicefs-volume || (echo \"install juicefs-volume\" && docker plugin install --alias juicefs-volume jakubs22/juicefs:arm64-latest --grant-all-permissions && echo \"installed\" && docker plugin disable juicefs-volume && echo \"disabled juicefs-volume\" && docker plugin enable juicefs-volume && echo \"enabled juicefs-volume again\")) && echo \"done\""
    networks:
      - bridge
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    
    deploy:
      mode: global
      restart_policy:
        condition: "none"

      resources:
        limits:
          cpus: "0.5"
          memory: 512M

networks:
  bridge:
    external: true
    name: bridge