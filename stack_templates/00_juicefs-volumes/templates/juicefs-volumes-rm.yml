services:
  docker_volume_juicefs:
    image: docker
    command: sh -c "docker plugin disable juicefs-volume && docker plugin rm juicefs-volume"
    networks:
      - bridge
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    
    deploy:
      mode: global
      restart_policy:
        condition: "none"

      resources:
        limits:
          cpus: "0.5"
          memory: 512M

networks:
  bridge:
    external: true
    name: bridge