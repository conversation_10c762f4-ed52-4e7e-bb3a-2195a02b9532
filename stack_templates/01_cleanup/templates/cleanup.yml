services:
  system-prune:
    image: docker
    command: docker system prune -af --filter "until=24h" --filter "label!=com.docker.swarm.task"
    networks:
      - bridge
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      mode: global
      placement:
        constraints: [node.platform.os == linux]
      restart_policy:
        # daily
        delay: 86400s
      resources:
        limits:
          cpus: "0.5"
          memory: 512M

  volume-prune:
    image: docker
    command: docker volume prune -f
    networks:
      - bridge
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      mode: global
      placement:
        constraints: [ node.platform.os == linux ]
      restart_policy:
        # daily
        delay: 86400s
      resources:
        limits:
          cpus: "0.5"
          memory: 512M

networks:
  bridge:
    external: true
    name: bridge
