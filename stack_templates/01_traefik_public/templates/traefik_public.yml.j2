services:
  traefik_ingress:
    image: traefik:v3.0
    
    networks:
      - docker_socket_proxy
      - traefik-public

    healthcheck:
      test: ["CMD", "wget", "--tries=1", "--spider", "--quiet", "http://localhost:8080/ping"]
      interval: 10s
      timeout: 10s
      retries: 2
      start_period: 10s

    ports:
      - mode: host
        protocol: tcp
        published: 80
        target: 80
      - mode: host
        protocol: tcp
        published: 443
        target: 443
      - mode: host
        protocol: tcp
        published: 5432
        target: 5432
    
    command:
      - --providers.swarm.exposedByDefault=false
      - --providers.swarm.watch=true
      - --providers.swarm.endpoint=tcp://docker.socket.proxy.local:2375

      - --providers.file.directory=/etc/traefik/dynamic
      - --providers.file.watch=true

      - --entrypoints.web.address=:80
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https

      - --entrypoints.websecure.address=:443
      - --entrypoints.websecure.http3.advertisedPort=443
      - --entrypoints.websecure.http.tls.certresolver=cloudflare

      - --entrypoints.postgres.address=:5432

      - --certificatesresolvers.cloudflare.acme.dnschallenge=true
      - --certificatesresolvers.cloudflare.acme.dnschallenge.provider=cloudflare
      - --certificatesresolvers.cloudflare.acme.email={{ cf_api_email }}
      - --certificatesresolvers.cloudflare.acme.storage=/letsencrypt/acme.json

      - --certificatesresolvers.cloudflare-staging.acme.dnschallenge=true
      - --certificatesresolvers.cloudflare-staging.acme.dnschallenge.provider=cloudflare
      - --certificatesresolvers.cloudflare-staging.acme.email={{ cf_api_email }}
      - --certificatesresolvers.cloudflare-staging.acme.storage=/letsencrypt/acme-staging.json
      - --certificatesresolvers.cloudflare-staging.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory

      - --entrypoints.http.forwardedHeaders.trustedIPs=127.0.0.1/32,10.0.0.0/8,***********/16
      - --metrics.prometheus=true
      - --metrics.prometheus.buckets=0.1,0.3,1.2,5.0

      - --ping
      - --accesslog
      - --log
      - --api

    volumes:
      - letsencrypt:/letsencrypt
    configs:
      - source: tls_yml
        target: /etc/traefik/dynamic/tls.yml
    secrets:
      - source: cf_dns_api_token
        target: /run/secrets/cf_dns_api_token
    environment:
      - CF_DNS_API_TOKEN_FILE=/run/secrets/cf_dns_api_token
      - CF_API_EMAIL={{ cf_api_email }}

    deploy:
      mode: replicated
      replicas: 1
      update_config:
        order: stop-first
        parallelism: 1
      rollback_config:
        order: stop-first
        parallelism: 1
      restart_policy:
        condition: on-failure

      resources:
        limits:
          cpus: "2"
          memory: 4096M
        reservations:
          cpus: "0.5"
          memory: 1024M
      labels:
        - traefik.enable=true
        - traefik.http.services.traefik-public.loadbalancer.server.port=8080
        - traefik.http.routers.traefik-public-https.rule=Host(`{{ traefik_domain }}`)
        - traefik.http.routers.traefik-public-https.entrypoints=websecure
        - traefik.http.routers.traefik-public-https.tls.certresolver=cloudflare
        - traefik.http.routers.traefik-public-https.service=api@internal

      placement:
        preferences:
          - spread: node.labels.host
        # so that the replacement can come up at all
        max_replicas_per_node: 1
        constraints:
          - node.hostname == netcup-manager-1

configs:
  tls_yml:
    file: ./configs/tls.yml

secrets:
  cf_dns_api_token:
    file: ./secrets/cf_dns_api_token

networks:
  traefik-public:
    driver: overlay
    attachable: true
    name: traefik-public
    driver_opts:
      com.docker.network.driver.mtu: "1150"
  docker_socket_proxy:
    name: "docker_socket_proxy"
    external: true

volumes:
  letsencrypt:
    driver: juicefs-volume
    driver_opts:
      name: {{ vol_name }}
      subdir: letsencrypt
      token: {{ jfs_token }}
      access-key: {{ access_key }}
      secret-key: {{ secret_key }}