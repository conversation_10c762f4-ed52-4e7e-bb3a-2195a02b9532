route:
  receiver: 'default'
  group_by: ['alertname']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  routes:
    - match:
        severity: critical
      receiver: 'discord-critical'
      continue: true
      routes:
        - match_re:
            alertname: '^(node_down|manager_not_reachable|Container.*died.*|Container.*Too Many Restarts)$'
          receiver: 'sms-critical'
          continue: false
    - match:
        severity: warning
      receiver: 'discord-warning'

receivers:
{% if promswarm_discord_webhook_critical and promswarm_discord_webhook_critical != '' %}
  - name: 'discord-critical'
    discord_configs:
      - webhook_url: '{{ promswarm_discord_webhook_critical }}'
        title: '🚨 CRITICAL: {{ '{{' }} .GroupLabels.alertname {{ '}}' }}'
        message: |
          **Alert:** {{ '{{' }} .GroupLabels.alertname {{ '}}' }}
          **Summary:** {{ '{{' }} range .Alerts {{ '}}' }}{{ '{{' }} .Annotations.summary {{ '}}' }}{{ '{{' }} end {{ '}}' }}
        send_resolved: true
{% else %}
  - name: 'discord-critical'
    webhook_configs:
      - url: 'https://httpbin.org/post'
        send_resolved: true
{% endif %}

{% if promswarm_discord_webhook_warning and promswarm_discord_webhook_warning != '' %}
  - name: 'discord-warning'
    discord_configs:
      - webhook_url: '{{ promswarm_discord_webhook_warning }}'
        title: '⚠️ WARNING: {{ '{{' }} .GroupLabels.alertname {{ '}}' }}'
        message: |
          **Alert:** {{ '{{' }} .GroupLabels.alertname {{ '}}' }}
          **Summary:** {{ '{{' }} range .Alerts {{ '}}' }}{{ '{{' }} .Annotations.summary {{ '}}' }}{{ '{{' }} end {{ '}}' }}
        send_resolved: true
{% else %}
  - name: 'discord-warning'
    webhook_configs:
      - url: 'https://httpbin.org/post'
        send_resolved: true
{% endif %}

{% if promswarm_twilio_account_sid and promswarm_twilio_account_sid != '' %}
  - name: 'sms-critical'
    webhook_configs:
      - url: 'http://promtotwilio:9876/send'
        send_resolved: false
{% else %}
  - name: 'sms-critical'
    webhook_configs:
      - url: 'https://httpbin.org/post'
        send_resolved: false
{% endif %}

  - name: 'default'
    webhook_configs:
      - url: 'https://httpbin.org/post'
        send_resolved: true