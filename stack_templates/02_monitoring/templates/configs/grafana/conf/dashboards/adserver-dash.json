{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 7, "links": [], "liveNow": false, "panels": [{"gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 35, "title": "Bid Rates", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 3, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 1}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum (rate(precx_bidding_requests[$__rate_interval]))", "legendFormat": "total rate bidding requests", "range": true, "refId": "A"}], "title": "total rate bidding requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 7, "x": 12, "y": 1}, "id": 14, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum by(creative_id) (precx_chosen_creatives)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Chosen Creatives", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 5, "x": 19, "y": 1}, "id": 11, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "9.1.6", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum (precx_events_stored{event_type=\"TRACKING#START\"})", "legendFormat": "Tracking Start", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum(precx_events_stored{event_type=\"TRACKING#FIRST_QUARTILE\"})", "hide": false, "legendFormat": "Tracking First Quartile", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum(precx_events_stored{event_type=\"TRACKING#MIDPOINT\"})", "hide": false, "legendFormat": "Tracking Midpoint", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum(precx_events_stored{event_type=\"TRACKING#THIRD_QUARTILE\"})", "hide": false, "legendFormat": "Tracking Third Quartile", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum(precx_events_stored{event_type=\"TRACKING#COMPLETE\"})", "hide": false, "legendFormat": "Tracking Complete", "range": true, "refId": "B"}], "title": "Tracking Overview", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 19, "y": 6}, "id": 4, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum by(event_type) (precx_events_stored{event_type=~\"api-ad-stele-request|api-ad-stele-response\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Request/Response ratio", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 11}, "id": 39, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "builder", "expr": "sum(rate(precx_fallback_ads[$__rate_interval]))", "legendFormat": "Fallback Ads", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(precx_ssp_ads[$__rate_interval])) OR on() vector(0)", "hide": false, "legendFormat": "SSP Ads", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(precx_cms_ads[$__rate_interval])) OR on() vector(0)", "hide": false, "legendFormat": "CMS Ads", "range": true, "refId": "D"}], "title": "[DRAFT]Rates: Fallback vs SSP vs VAST Ads vs CMS Ads", "type": "timeseries"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "2023-02-24T23:40:17.122Z", "to": "2023-02-28T08:19:14.399Z"}, "timepicker": {}, "timezone": "", "title": "Precision X - Systems Overview", "uid": "CFbrg014y", "version": 1, "weekStart": ""}