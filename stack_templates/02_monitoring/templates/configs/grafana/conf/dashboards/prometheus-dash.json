{"annotations": {"list": [{"$$hashKey": "object:698", "builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "links": [{"icon": "info", "tags": [], "targetBlank": true, "title": "<PERSON>ana <PERSON>s", "tooltip": "", "type": "link", "url": "http://docs.grafana.org/"}, {"icon": "info", "tags": [], "targetBlank": true, "title": "Prometheus Docs", "type": "link", "url": "http://prometheus.io/docs/introduction/overview/"}], "panels": [{"aliasColors": {"prometheus": "#C15C17", "{instance=\"localhost:9090\",job=\"prometheus\"}": "#CCA300"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(prometheus_tsdb_head_samples_appended_total{job=\"prometheus\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "samples", "metric": "", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Sam<PERSON> Appended", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(5, max(scrape_duration_seconds) by (job))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{job}}", "metric": "", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Scrape Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "", "fill": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(process_resident_memory_bytes{job=\"prometheus\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "p8s process resident memory", "refId": "D", "step": 20}, {"expr": "process_virtual_memory_bytes{job=\"prometheus\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "virtual memory", "refId": "C", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory Profile", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transparent": false, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 37, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "prometheus_tsdb_wal_corruptions_total{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 60}], "thresholds": "0.1,1", "title": "WAL Corruptions", "type": "singlestat", "valueFontSize": "200%", "valueMaps": [{"op": "=", "text": "None", "value": "0"}], "valueName": "max"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 5}, "id": 29, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(prometheus_tsdb_head_active_appenders{job=\"prometheus\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "active_appenders", "metric": "", "refId": "A", "step": 20}, {"expr": "sum(process_open_fds{job=\"prometheus\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "open_fds", "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Active Appenders", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {"prometheus": "#F9BA8F", "{instance=\"localhost:9090\",interval=\"5s\",job=\"prometheus\"}": "#F9BA8F"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 5}, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_blocks_loaded{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "blocks", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Blocks Loaded", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": null, "description": "", "fill": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 5}, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_chunks{job=\"prometheus\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "chunks", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Chunks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "bytes", "label": "", "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 5}, "id": 36, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "duration-p99", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_gc_duration_seconds{job=\"prometheus\",quantile=\"0.99\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "duration-p99", "refId": "A", "step": 20}, {"expr": "irate(prometheus_tsdb_head_gc_duration_seconds_count{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "collections", "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Block GC Activity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": null, "description": "", "fill": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 10}, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "duration-p99", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(prometheus_tsdb_compaction_duration_bucket{job=\"prometheus\"}[5m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "duration-{{p99}}", "refId": "A", "step": 20}, {"expr": "irate(prometheus_tsdb_compactions_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "compactions", "refId": "B", "step": 20}, {"expr": "irate(prometheus_tsdb_compactions_failed_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "failed", "refId": "C", "step": 20}, {"expr": "irate(prometheus_tsdb_compactions_triggered_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "triggered", "refId": "D", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compaction Activity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 10}, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_reloads_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "reloads", "refId": "A", "step": 20}, {"expr": "rate(prometheus_tsdb_reloads_failures_total{job=\"prometheus\"}[5m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "failures", "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Reload Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 10}, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_engine_query_duration_seconds{job=\"prometheus\", quantile=\"0.99\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{slice}}_p99", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Query Durations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": null, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "id": 35, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(prometheus_rule_group_duration_seconds{job=\"prometheus\"}) by (quantile)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{quantile}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Rule Group Eval Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "id": 39, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(prometheus_rule_group_iterations_missed_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "missed", "refId": "B", "step": 10}, {"expr": "rate(prometheus_rule_group_iterations_total{job=\"prometheus\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "iterations", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Rule Group Eval Activity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "refresh": "1m", "revision": "1.0", "schemaVersion": 16, "style": "dark", "tags": ["prometheus"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Prometheus 2.0 Stats", "uid": "mGFfYSRiz", "version": 1}