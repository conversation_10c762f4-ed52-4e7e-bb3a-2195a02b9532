{"annotations": {"list": [{"$$hashKey": "object:429", "builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Docker Swarm stacks and services metrics", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "iteration": 1655486948760, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "hideTimeOverride": true, "id": 1, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.5", "targets": [{"expr": "count(count(container_tasks_state{container_label_com_docker_swarm_node_id =~\"$node_id\"}) by (container_label_com_docker_swarm_node_id))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Nodes", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "hideTimeOverride": true, "id": 21, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.5", "targets": [{"expr": "count(count(container_tasks_state{container_label_com_docker_stack_namespace=~\".+\", container_label_com_docker_swarm_node_id=~\"$node_id\"}) by (container_label_com_docker_stack_namespace))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Stacks", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "hideTimeOverride": true, "id": 20, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.5", "targets": [{"expr": "count(count(container_tasks_state{container_label_com_docker_swarm_service_name=~\".+\", container_label_com_docker_swarm_node_id=~\"$node_id\"}) by (container_label_com_docker_swarm_service_name))", "format": "time_series", "intervalFactor": 2, "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Services", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "hideTimeOverride": true, "id": 7, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.5", "targets": [{"expr": "count(rate(container_last_seen{container_label_com_docker_swarm_node_id=~\"$node_id\"}[5m])) ", "format": "time_series", "intervalFactor": 2, "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Containers", "type": "stat"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "decimals": 0, "fill": 5, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 4}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(container_last_seen{container_label_com_docker_swarm_node_id=~\"$node_id\"}[5m])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 10, "legendFormat": "{{ container_label_com_docker_swarm_service_name }}", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Service Tasks", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 4}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": false, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(engine_daemon_health_checks_total[$interval]) * on(instance) group_left(node_id) swarm_node_info{node_id=~\"$node_id\"})  ", "format": "time_series", "intervalFactor": 10, "legendFormat": "checks", "refId": "A", "step": 10}, {"expr": "sum(increase(engine_daemon_health_checks_failed_total[$interval]) * on(instance) group_left(node_id) swarm_node_info{node_id=~\"$node_id\"})  ", "format": "time_series", "intervalFactor": 10, "legendFormat": "failed", "refId": "B", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Health Checks", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "decimals": 2, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 20, "x": 0, "y": 11}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(irate(container_cpu_usage_seconds_total{container_label_com_docker_swarm_node_id=~\"$node_id\", id=~\"/docker/.*\"}[1m])) by (container_label_com_docker_swarm_service_name) * 100 ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}}", "refId": "A", "step": 2}], "thresholds": [], "timeRegions": [], "title": "CPU usage by Service", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 25}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 11}, "hideTimeOverride": true, "id": 11, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.5.5", "targets": [{"expr": "sum(irate(node_cpu_seconds_total{mode=\"idle\"}[$interval]) * on(instance) group_left(node_name) node_meta{node_id=~\"$node_id\"}) * 100 / count(node_cpu_seconds_total{mode=\"user\"} * on(instance) group_left(node_name) node_meta{node_id=~\"$node_id\"}) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 2}], "timeFrom": "1m", "title": "CPU Idle", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "decimals": 2, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 18}, "hiddenSeries": false, "id": 33, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(10, sum(irate(container_cpu_usage_seconds_total{container_label_com_docker_swarm_node_id=~\"$node_id\", id=~\"/docker/.*\"}[$interval])) by (name)) * 100 ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 2}], "thresholds": [], "timeRegions": [], "title": "CPU usage by Container (top 10)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 20, "x": 0, "y": 25}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(container_memory_usage_bytes{container_label_com_docker_swarm_node_id=~\"$node_id\", id=~\"/docker/.*\"}) by (container_label_com_docker_swarm_service_name) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "Used {{container_label_com_docker_swarm_service_name}}", "refId": "A", "step": 2}, {"expr": "sum(container_memory_cache{container_label_com_docker_swarm_node_id=~\"$node_id\", id=~\"/docker/.*\"}) by (container_label_com_docker_swarm_service_name) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "Cached {{container_label_com_docker_swarm_service_name}}", "refId": "B", "step": 2}], "thresholds": [], "timeRegions": [], "title": "Memory usage by Service", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "decbytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 25}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 25}, "id": 8, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.5.5", "targets": [{"expr": "sum((node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) * on(instance) group_left(node_name) node_meta{node_id=~\"$node_id\"} * 100) / count(node_meta * on(instance) group_left(node_name) node_meta{node_id=~\"$node_id\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "title": "Available Memory", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 32}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(10, avg_over_time(container_memory_usage_bytes{container_label_com_docker_swarm_node_id=~\"$node_id\", id=~\"/docker/.*\"}[$interval]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 2}], "thresholds": [], "timeRegions": [], "title": "Memory usage by Container (top 10)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "decbytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 39}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{container_label_com_docker_swarm_node_id=~\"$node_id\"}[$interval])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}}", "refId": "A", "step": 2}], "thresholds": [], "timeRegions": [], "title": "Network received by Service", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Bps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 46}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_transmit_bytes_total{container_label_com_docker_swarm_node_id=~\"$node_id\"}[$interval])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}}", "metric": "", "refId": "B", "step": 2}], "thresholds": [], "timeRegions": [], "title": "Network transmitted by Service", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Bps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 10, "x": 0, "y": 53}, "hiddenSeries": false, "id": 31, "legend": {"avg": true, "current": false, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{id=\"/\"}[$interval])) by (id)", "format": "time_series", "intervalFactor": 2, "legendFormat": "Received", "refId": "A", "step": 4}, {"expr": "- sum(rate(container_network_transmit_bytes_total{id=\"/\"}[$interval])) by (id)", "format": "time_series", "intervalFactor": 2, "legendFormat": "Transmited", "refId": "B", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Cluster Network Traffic", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Bps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 10, "x": 10, "y": 53}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": false, "avg": true, "current": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(container_fs_reads_total[$interval]) )", "format": "time_series", "intervalFactor": 2, "legendFormat": "Reads", "refId": "A", "step": 4}, {"expr": "sum(irate(container_fs_writes_total[$interval])) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "Writes ", "refId": "B", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Cluster IOPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 25}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 53}, "id": 27, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.5.5", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "sum((node_filesystem_free_bytes{mountpoint=\"/\"} / node_filesystem_size_bytes{mountpoint=\"/\"}) * on(instance) group_left(node_name) node_meta{node_id=~\"$node_id\"} * 100) / count(node_meta * on(instance) group_left(node_name) node_meta{node_id=~\"$node_id\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "title": "Available Disk Space", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 60}, "hiddenSeries": false, "id": 29, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(engine_daemon_container_actions_seconds_count * on(instance) group_left(node_id) swarm_node_info{node_id=~\"$node_id\"})  by (action)", "format": "time_series", "intervalFactor": 10, "legendFormat": "{{action }}", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Docker Daemon Container Actions", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 60}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(engine_daemon_network_actions_seconds_count * on(instance) group_left(node_id) swarm_node_info{node_id=~\"$node_id\"})  by (action)", "format": "time_series", "intervalFactor": 10, "legendFormat": "{{action }}", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Docker Daemon Network Actions", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"columns": [{"$$hashKey": "object:71", "text": "Avg", "value": "avg"}], "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 67}, "hideTimeOverride": true, "id": 28, "links": [], "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "sum(engine_daemon_engine_info * on(instance) group_left(node_id) swarm_node_info) by (kernel, os, graphdriver, version, node_id)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 2}], "timeFrom": "1s", "title": "Docker Engine Info", "transform": "timeseries_to_rows", "type": "table-old"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}, "id": 100, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"expr": "sum(container_fs_usage_bytes{container_label_com_docker_swarm_service_name!=\"\"}) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}}", "refId": "A", "step": 10}], "title": "Disk Usage by Service", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 101, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Value"}]}, "pluginVersion": "8.5.5", "targets": [{"expr": "topk(10, sum(container_fs_usage_bytes{container_label_com_docker_swarm_service_name!=\"\"}) by (container_label_com_docker_swarm_service_name))", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Top 10 Services by Disk Usage", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"container_label_com_docker_swarm_service_name": "Service", "Value": "Disk Usage"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binBps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}, "id": 102, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"expr": "sum(rate(container_fs_reads_bytes_total[5m])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}} - Read", "refId": "A", "step": 10}, {"expr": "sum(rate(container_fs_writes_bytes_total[5m])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}} - Write", "refId": "B", "step": 10}], "title": "Disk I/O by Service", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 103, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"expr": "sum(rate(container_fs_reads_total[5m])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}} - Read Ops", "refId": "A", "step": 10}, {"expr": "sum(rate(container_fs_writes_total[5m])) by (container_label_com_docker_swarm_service_name)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{container_label_com_docker_swarm_service_name}} - Write Ops", "refId": "B", "step": 10}], "title": "Disk IOPS by Service", "type": "timeseries"}], "refresh": false, "schemaVersion": 36, "style": "dark", "tags": ["swarmprom"], "templating": {"list": [{"allValue": ".+", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "definition": "", "hide": 0, "includeAll": true, "label": "Swarm Node", "multi": false, "name": "node_id", "options": [], "query": {"query": "node_meta", "refId": "Prometheus-node_id-Variable-Query"}, "refresh": 2, "regex": "/node_id=\"([^\"]+)\"/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"auto": true, "auto_count": 30, "auto_min": "30s", "current": {"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Docker Swarm Services", "uid": "zr_baSRmk", "version": 1, "weekStart": ""}