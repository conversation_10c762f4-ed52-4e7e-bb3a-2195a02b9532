{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Traefik dashboard prometheus", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 4475, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "refId": "A"}], "title": "$service stats", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"1": {"text": "OK"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 8, "x": 0, "y": 1}, "id": 1, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.7", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "count(traefik_service_open_connections)", "format": "time_series", "intervalFactor": 2, "refId": "A"}], "title": "Number of Traefik Services ", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "max": 5, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 3}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 8, "x": 8, "y": 1}, "id": 2, "links": [], "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.0.7", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "traefik_service_request_duration_seconds_sum{service=\"$service\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{$service}} | {{method}}", "refId": "A"}], "title": "Average response time", "type": "gauge"}, {"aliasColors": {}, "breakPoint": "50%", "combine": {"label": "Others", "threshold": 0}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fontSize": "80%", "format": "short", "gridPos": {"h": 11, "w": 8, "x": 16, "y": 1}, "id": 4, "legend": {"show": true, "values": true}, "legendType": "Under graph", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "pieType": "pie", "pluginVersion": "6.5.0", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "avg_over_time(traefik_service_request_duration_seconds_sum{service=\"$service\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{service}}", "refId": "A"}], "title": "Average Service response time", "type": "grafana-piechart-panel", "valueName": "current"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 12}, "id": 3, "links": [], "options": {"displayMode": "basic", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["range"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "9.0.7", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "avg(traefik_service_requests_total{service=\"$service\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{$service}} | {{method}}", "refId": "A"}], "title": "Total requests over 5min $service", "type": "bargauge"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 12, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "refId": "A"}], "title": "Global stats", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "rate(traefik_entrypoint_requests_total{entrypoint=~\"$entrypoint\",code=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Status code 200 over 5min", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "id": 6, "links": [], "options": {"displayMode": "basic", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "limit": 10, "values": false}, "showUnfilled": true}, "pluginVersion": "9.0.7", "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "rate(traefik_entrypoint_requests_total{entrypoint=~\"$entrypoint\",code!=\"200\"}[5m]) * 100", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{ method }} : {{code}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "avg_over_time", "refId": "B"}], "title": "Entrypoint Error Codes", "type": "bargauge"}, {"aliasColors": {}, "breakPoint": "50%", "combine": {"label": "Others", "threshold": 0}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fontSize": "80%", "format": "short", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 27}, "id": 7, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "sum(rate(traefik_service_requests_total[5m])) by (service) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ service }}", "refId": "A"}], "title": "Requests by service", "type": "grafana-piechart-panel", "valueName": "total"}, {"aliasColors": {}, "breakPoint": "50%", "combine": {"label": "Others", "threshold": 0}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fontSize": "80%", "format": "short", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 27}, "id": 8, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 3, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "expr": "sum(rate(traefik_entrypoint_requests_total{entrypoint =~ \"$entrypoint\"}[5m])) by (entrypoint) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ entrypoint }}", "refId": "A"}], "title": "Requests by protocol", "type": "grafana-piechart-panel", "valueName": "total"}], "schemaVersion": 36, "style": "dark", "tags": ["traefik", "prometheus"], "templating": {"list": [{"current": {"selected": true, "text": "website-frontend-prod@docker", "value": "website-frontend-prod@docker"}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "definition": "label_values(service)", "hide": 0, "includeAll": true, "multi": false, "name": "service", "options": [], "query": {"query": "label_values(service)", "refId": "Prometheus-service-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": ["http"], "value": ["http"]}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "definition": "label_values(entrypoint)", "hide": 0, "includeAll": true, "multi": true, "name": "entrypoint", "options": [], "query": {"query": "label_values(entrypoint)", "refId": "Prometheus-entrypoint-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "200", "value": "200"}, "datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "definition": "label_values(code)", "hide": 2, "includeAll": false, "multi": false, "name": "code", "options": [], "query": {"query": "label_values(code)", "refId": "Prometheus-code-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Traefik2", "uid": "qPdAviJmz1", "version": 1, "weekStart": ""}