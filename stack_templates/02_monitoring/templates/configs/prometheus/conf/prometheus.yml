global:
  scrape_interval:     15s
  evaluation_interval: 15s

  external_labels:
    monitor: 'promswarm'

rule_files:
  - "swarm_node.rules.yml"
  - "swarm_task.rules.yml"
  - "service_dns.rules.yml"

alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'cadvisor'
    dns_sd_configs:
    - names:
      - 'tasks.cadvisor'
      type: 'A'
      port: 8080

  - job_name: 'node-exporter'
    dns_sd_configs:
    - names:
      - 'tasks.node-exporter'
      type: 'A'
      port: 9100

  - job_name: 'traefik'

    # Override the global default and scrape targets from this job every 5 seconds.
    scrape_interval: 5s

    dns_sd_configs:
    - names:
      - 'tasks.traefik_ingress'
      type: 'A'
      port: 8080

  - job_name: 'docker-engine-events-exporter'
    dns_sd_configs:
    - names:
      - 'tasks.docker-engine-events-exporter'
      type: 'A'
      port: 9000
      
  - job_name: 'docker-engine-networks-exporter'
    dns_sd_configs:
    - names:
      - 'tasks.docker-engine-networks-exporter'
      type: 'A'
      port: 9000

  - job_name: 'docker-swarm-exporter'
    dns_sd_configs:
    - names:
      - 'tasks.docker-swarm-exporter'
      type: 'A'
      port: 9000

  - job_name: prometheus-proxy
    dns_sd_configs:
    - names:
      - prometheus-proxy.local
      type: 'A'
      port: 3000

  - job_name: 'docker-service-dns-prometheus-exporter'
    dns_sd_configs:
    - names:
      - 'tasks.docker-service-dns-prometheus-exporter'
      type: 'A'
      port: 9000
