groups:
- name: /1/store/projects/docker-swarm/apps/swarmprom/prometheus/rules/swarm_task.rules.yml
  rules:
  - alert: task_high_cpu_usage_50
    expr: sum(rate(container_cpu_usage_seconds_total{container_label_com_docker_swarm_task_name=~".+"}[1m]))
      BY (container_label_com_docker_swarm_task_name, container_label_com_docker_swarm_node_id)
      * 100 > 50
    for: 15m
    annotations:
      description: '{{ $labels.container_label_com_docker_swarm_task_name }} on ''{{
        $labels.container_label_com_docker_swarm_node_id }}'' CPU usage is at {{ humanize
        $value}}%.'
      summary: CPU alert for Swarm task '{{ $labels.container_label_com_docker_swarm_task_name
        }}' on '{{ $labels.container_label_com_docker_swarm_node_id }}'
  
  - alert: task_high_memory_usage_1g
    expr: (
          sum(container_memory_rss{container_label_com_docker_swarm_task_name=~".+"})
            BY (container_label_com_docker_swarm_task_name, container_label_com_docker_swarm_node_id) > 1e+09
          AND 
          ((sum(container_spec_memory_limit_bytes{container_label_com_docker_swarm_task_name=~".+"})
              BY (container_label_com_docker_swarm_task_name, container_label_com_docker_swarm_node_id)) == 0)
      )
    for: 1m
    annotations:
      description: '{{ $labels.container_label_com_docker_swarm_task_name }} on ''{{
        $labels.container_label_com_docker_swarm_node_id }}'' memory usage is {{ humanize
        $value}}.'
      summary: Memory alert for Swarm task '{{ $labels.container_label_com_docker_swarm_task_name
        }}' on '{{ $labels.container_label_com_docker_swarm_node_id }}'

  - alert: task_high_memory_usage_limit
    expr: (
          (
              (sum(container_memory_rss{container_label_com_docker_swarm_task_name=~".+"})
                  BY (container_label_com_docker_swarm_task_name, container_label_com_docker_swarm_node_id))
              >
              (
                  (sum(container_spec_memory_limit_bytes{container_label_com_docker_swarm_task_name=~".+"})
                  BY (container_label_com_docker_swarm_task_name, container_label_com_docker_swarm_node_id))
                  *
                  0.8
              )
          )
          AND 
          ((sum(container_spec_memory_limit_bytes{container_label_com_docker_swarm_task_name=~".+"})
              BY (container_label_com_docker_swarm_task_name, container_label_com_docker_swarm_node_id)) > 0)
      )
    for: 1m
    annotations:
      description: '{{ $labels.container_label_com_docker_swarm_task_name }} on ''{{
        $labels.container_label_com_docker_swarm_node_id }}'' memory usage is {{ humanize
        $value}} which is more than 80% of the summed up limit of all containers.'
      summary: Memory alert for Swarm task '{{ $labels.container_label_com_docker_swarm_task_name
        }}' on '{{ $labels.container_label_com_docker_swarm_node_id }}'

  - alert: Container (Swarm) Too Many Restarts
    expr: count by (instance, container_label_com_docker_swarm_service_name, image) (count_over_time(container_last_seen{container_label_com_docker_swarm_service_name!=""}[15m])) - 1 >= 10
    for: 5m
    annotations:
      summary: "Too many restarts ({{ $value }}) for container \"{{ $labels.container_label_com_docker_swarm_service_name }}\" and docker image \"{{ $labels.image }}\""

  - alert: Container (Swarm) died/is dying with exit code other than 0
    expr: count by (docker_hostname, container_attributes_com_docker_swarm_service_name, container_attributes_exitcode, status) (
          (
              docker_events_container_total{status=~"die|.*oom.*|.*kill.*", container_attributes_exitcode != "0", container_attributes_exitcode != "" } 
              unless 
              docker_events_container_total{status=~"die|.*oom.*|.*kill.*", container_attributes_exitcode != "0", container_attributes_exitcode != "" }
              offset 10m
          ) OR (
              increase(docker_events_container_total{status=~"die|.*oom.*|.*kill.*", container_attributes_exitcode != "0", container_attributes_exitcode != "" }[10m]) > 0
          )
      )
    annotations:
      summary: "Bad Exit code \"{{ $labels.container_attributes_exitcode }}\" for status \"{{ $labels.status }}\" for service \"{{ $labels.container_attributes_com_docker_swarm_service_name }}\""