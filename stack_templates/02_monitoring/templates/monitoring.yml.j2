version: "3.8"

services:
  prometheus:
    user: root
    entrypoint: "/etc/prometheus/docker-entrypoint.sh"
    image:  ghcr.io/jakvbs/prometheus:v2.41.0
    healthcheck:
      test: ["CMD", "wget", "--tries=1", "--spider", "--quiet", "http://localhost:9090/metrics"]
      interval: 10s
      timeout: 10s
      retries: 2
      start_period: 10s
    networks:
      - net
      - external_monitors
      - traefik-public
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention=${PROMETHEUS_RETENTION:-720h}'
      - '--web.external-url=https://prometheus-${BASE_DOMAIN}'
    volumes:
      - prometheus:/prometheus
    configs:
      - source: dns_rules
        target: /etc/prometheus/service_dns.rules.yml
      - source: node_rules
        target: /etc/prometheus/swarm_node.rules.yml
      - source: task_rules
        target: /etc/prometheus/swarm_task.rules.yml
      - source: prometheus_entrypoint
        target: /etc/prometheus/docker-entrypoint.sh
        mode: 0777
      - source: prometheus_yml
        target: /etc/prometheus/prometheus.yml.template
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          memory: 3072M
        reservations:
          memory: 128M
      placement:
        constraints:
          - node.hostname == netcup-manager-1
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.http.routers.monitoring-prometheus-http.rule=Host(`prometheus-${BASE_DOMAIN}`) && !PathPrefix(`/metrics`) && !PathPrefix(`/healhtz`)
        - traefik.http.routers.monitoring-prometheus-http.entrypoints=websecure
        - traefik.http.routers.monitoring-prometheus-http.tls.certresolver=cloudflare
        - traefik.http.services.monitoring-prometheus.loadbalancer.server.port=9090

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    healthcheck:
      test: ["CMD", "wget", "--tries=1", "--spider", "--quiet", "http://localhost:8080/metrics"]
      interval: 10s
      timeout: 10s
      retries: 2
      start_period: 10s
    networks:
      - net
    command: -logtostderr -docker_only
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /:/rootfs:ro
      - /var/run:/var/run
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    deploy:
      mode: global
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 64M

  docker-engine-events-exporter:
    image: jakubs22/docker-engine-events-exporter:latest
    init: true
    healthcheck:
      disable: true
    networks:
      - net
    environment:
{% raw %}
      - DOCKER_HOSTNAME={{.Node.Hostname}}
{% endraw %}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    deploy:
      mode: global
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 0
        window: 120s
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  docker-engine-networks-exporter:
    image: jakubs22/docker-engine-networks-exporter:latest
    init: true
    healthcheck:
      disable: true
    networks:
      - net
    environment:
{% raw %}
      - DOCKER_HOSTNAME={{.Node.Hostname}}
{% endraw %}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    deploy:
      mode: global
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 0
        window: 120s
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  docker-swarm-exporter:
    image: jakubs22/docker-swarm-exporter:latest
    init: true
    healthcheck:
      disable: true
    networks:
      - net
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 0
        window: 120s
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
      placement:
        constraints:
          - node.role==manager

  node-exporter:
    image: ghcr.io/jakvbs/node-exporter:latest
    entrypoint: /etc/node-exporter/docker-entrypoint.sh
    init: true
    healthcheck:
      disable: true
    user: root
    networks:
      - net
    environment:
{% raw %}
      - NODE_ID={{.Node.ID}}
{% endraw %}
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
      - /etc/hostname:/etc/nodename
    command:
      - '--path.sysfs=/host/sys'
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--collector.textfile.directory=/etc/node-exporter/'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
      - '--no-collector.ipvs'
    configs:
      - source: nodeexporter_entrypoint
        target: /etc/node-exporter/docker-entrypoint.sh
        mode: 0777
    deploy:
      mode: global
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 64M

  grafana:
    entrypoint: /custom_entrypoint.sh
    image: grafana/grafana:9.3.6
    healthcheck:
      test: ["CMD", "wget", "--tries=1", "--spider", "--quiet", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 10s
      retries: 2
      start_period: 10s
    user: root
    networks:
      - net
      - traefik-public
    environment:
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana:/var/lib/grafana
    secrets:
      - source: grafana_environment_sh
        target: grafana_environment_sh
    configs:
      - source: grafana_traefik_dash
        target: /etc/grafana/dashboards/traefik-dash.json
      - source: grafana_nodes_dash
        target: /etc/grafana/dashboards/nodes-dash.json
      - source: grafana_prometheus_dash
        target: /etc/grafana/dashboards/prometheus-dash.json
      - source: grafana_services_dash
        target: /etc/grafana/dashboards/services-dash.json

      - source: grafana_dashboards
        target: /etc/grafana/provisioning/dashboards/dashboards.yaml

      - source: grafana_datasource_prometheus
        target: /etc/grafana/provisioning/datasources/prometheus.yaml

      - source: grafana_datasource_entrypoint
        target: /custom_entrypoint.sh
        mode: 0777
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.hostname == netcup-manager-1
      resources:
        limits:
          memory: 1024M
        reservations:
          memory: 128M
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.http.routers.monitoring-grafana-http.rule=Host(`grafana-${BASE_DOMAIN}`) && !PathPrefix(`/api/health`) && !PathPrefix(`/metrics`) && !PathPrefix(`/healhtz`)
        - traefik.http.routers.monitoring-grafana-http.entrypoints=websecure
        - traefik.http.routers.monitoring-grafana-http.tls.certresolver=cloudflare
        - traefik.http.services.monitoring-grafana.loadbalancer.server.port=3000

  alertmanager:
    image: ghcr.io/jakvbs/alertmanager:v0.25.0
    healthcheck:
      test: ["CMD", "wget", "--tries=1", "--spider", "--quiet", "http://localhost:9093/metrics"]
      interval: 10s
      timeout: 10s
      retries: 2
      start_period: 10s
    networks:
      - net
      - traefik-public
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=https://alertmanager-${BASE_DOMAIN}'
      - '--data.retention=${ALERTMANAGER_RETENTION:-720h}'
    volumes:
      - alertmanager:/alertmanager
    configs:
      - source: alertmanager_yml
        target: /etc/alertmanager/alertmanager.yml
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.hostname == netcup-manager-1
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 64M
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.http.routers.monitoring-alertmanager-http.rule=Host(`alertmanager-${BASE_DOMAIN}`) && !PathPrefix(`/metrics`) && !PathPrefix(`/healhtz`)
        - traefik.http.routers.monitoring-alertmanager-http.entrypoints=websecure
        - traefik.http.routers.monitoring-alertmanager-http.tls.certresolver=cloudflare
        - traefik.http.services.monitoring-alertmanager.loadbalancer.server.port=9093

  # SMS Gateway for critical alerts (Promtotwilio)
  promtotwilio:
    image: ghcr.io/jakvbs/promtotwilio:latest
    networks:
      - net
    environment:
      SID: ${TWILIO_ACCOUNT_SID}
      TOKEN: ${TWILIO_AUTH_TOKEN}
      SENDER: ${TWILIO_FROM_NUMBER}
      RECEIVER: ${SMS_ALERT_PHONE_NUMBERS}
      PORT: 9876
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          memory: 64M
        reservations:
          memory: 16M

  docker_socket_proxy:
    image: tecnativa/docker-socket-proxy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - net
    environment:
      CONTAINERS: 1
      SERVICES: 1
      SWARM: 1
      NODES: 1
      NETWORKS: 1
      TASKS: 1
      VERSION: 1

      AUTH: 1
      SECRETS: 1
      POST: 1
      BUILD: 1
      COMMIT: 1
      CONFIGS: 1
      DISTRIBUTION: 1
      EXEC: 1
      GRPC: 1
      IMAGES: 1
      INFO: 1
      PLUGINS: 1
      SESSION: 1
      SYSTEM: 1
      VOLUMES: 1
    deploy:
      mode: global

  docker-service-dns-prometheus-exporter:
    image: jakubs22/docker-service-dns-prometheus-exporter:dnscheck-latest
    environment:
      PROXY_SERVICE_NAME: monitoring_docker_socket_proxy
      DNS_CHECK_CONTAINER_IMAGE: jakubs22/docker-service-dns-prometheus-exporter:dnscheck-latest
      DEBUG: "true"
      SCRAPE_INTERVAL: "120"
      SERVICE_REGEX_IGNORE_LIST: |
        - ".*_backup$$"
        - ".*-cron-.*"
        - ".*_pgdump$$"
        - ".*_backup_"
    volumes:
      - '/var/run/docker.sock:/var/run/docker.sock'
    networks:
      - net
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
      placement:
        constraints:
          - node.role==manager

{#  jaeger:#}
{#    image: jaegertracing/all-in-one:latest#}
{#    networks:#}
{#      - net#}
{#      - traefik-public#}
{#    environment:#}
{#      - COLLECTOR_ZIPKIN_HOST_PORT=:9411#}
{#    ports:#}
{#      - "16686:16686"#}
{#      - "14268:14268"#}

{#  loki:#}
{#    image: grafana/loki:latest#}
{#    networks:#}
{#      - net#}
{#    volumes:#}
{#      - loki:/loki#}
{#    command: -config.file=/etc/loki/local-config.yaml#}

configs:
  dns_rules:
    file: ./configs/prometheus/rules/service_dns.rules.yml
  node_rules:
    file: ./configs/prometheus/rules/swarm_node.rules.yml
  task_rules:
    file: ./configs/prometheus/rules/swarm_task.rules.yml
  prometheus_entrypoint:
    file: ./configs/prometheus/conf/docker-entrypoint.sh
  prometheus_yml:
    file: ./configs/prometheus/conf/prometheus.yml

  alertmanager_yml:
    file: ./configs/alertmanager/alertmanager.yml

  nodeexporter_entrypoint:
    file: ./configs/node-exporter/conf/docker-entrypoint.sh

  grafana_traefik_dash:
    file: ./configs/grafana/conf/dashboards/traefik-dash.json
  grafana_nodes_dash:
    file: ./configs/grafana/conf/dashboards/nodes-dash.json
  grafana_prometheus_dash:
    file: ./configs/grafana/conf/dashboards/prometheus-dash.json
  grafana_services_dash:
    file: ./configs/grafana/conf/dashboards/services-dash.json
  grafana_datasource_prometheus:
    file: ./configs/grafana/conf/datasources/prometheus.yaml
  grafana_dashboards:
    file: ./configs/grafana/conf/dashboards.yaml
  grafana_datasource_entrypoint:
    file: ./configs/grafana/conf/docker-entrypoint.sh

secrets:
  grafana_environment_sh:
    file: ./secrets/grafana/environment.sh

networks:
  traefik-public:
    name: "{{ promswarm_traefik_public_network | default('traefik-public') }}"
    external: true
  net:
    driver: overlay
    attachable: true
    driver_opts:
      com.docker.network.driver.mtu: "{{ promswarm_monitoring_net_network_mtu | default('1150') }}"
  external_monitors:
    driver: overlay
    attachable: true
    driver_opts:
      com.docker.network.driver.mtu: "{{ promswarm_external_monitors_network_mtu | default('1150') }}"

volumes:
  prometheus:
    {{ promswarm_prometheus_volume_config | default({}) }}
  grafana:
    {{ promswarm_grafana_volume_config | default({}) }}
  alertmanager:
    {{ promswarm_alertmanager_volume_config | default({}) }}
