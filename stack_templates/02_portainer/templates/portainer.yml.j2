services:
  agent:
    image: portainer/agent:{{ portainer_version }}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volumes
    networks:
      - agent_network
    deploy:
      mode: global
      placement:
        constraints: [node.platform.os == linux]

      resources:
        limits:
          cpus: "1"
          memory: 1024M
        reservations:
          cpus: "0.5"
          memory: 512M

  portainer:
    image: portainer/portainer-ce:{{ portainer_version }}
    command: -H tcp://tasks.agent:9001 --tlsskipverify
    volumes:
      - portainer_data:/data
    networks:
      - agent_network
      - traefik-public
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.http.services.portainer.loadbalancer.server.port=9000
        - traefik.http.routers.portainer.rule=Host(`{{ portainer_domain }}`)
        - traefik.http.routers.portainer.entrypoints=websecure
        - traefik.http.routers.portainer.tls.certresolver=cloudflare
      
      placement:
        constraints:
          - node.hostname == netcup-manager-1

networks:
  agent_network:
    driver: overlay
    attachable: true
    driver_opts:
      com.docker.network.driver.mtu: "1150"
  traefik-public:
    external: true

volumes:
  portainer_data:
    driver: juicefs-volume
    driver_opts:
      name: {{ vol_name }}
      subdir: portainer_data
      token: {{ jfs_token }}
      access-key: {{ access_key }}
      secret-key: {{ secret_key }}
