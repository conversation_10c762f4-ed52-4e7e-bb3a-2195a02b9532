services:
  worker:
    image: ghcr.io/myoung34/docker-github-actions-runner:latest
    environment:
      ACCESS_TOKEN: {{ gh_access_token }}
      RUNNER_NAME: runner-{{'{{'}}.Task.Slot{{'}}'}}
      RUNNER_SCOPE: org
      ORG_NAME: skinbase-io
      EPHEMERAL: "true"
      DISABLE_AUTO_UPDATE: "true"
      UNSET_CONFIG_VARS: "true"
    deploy:
      replicas: 5
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
