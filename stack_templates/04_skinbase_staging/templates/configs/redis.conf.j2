# Redis configuration for Tailscale access

# Bind to container's network interface (will be accessible via Tailscale on host)
bind 0.0.0.0
protected-mode yes
port 6379

# Authentication
requirepass {{ redis_stage_password }}

# ACL Users
user default off
user skinbase on >{{ redis_stage_password }} ~* &* +@all

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Logging
loglevel notice

# Security - disable dangerous commands
rename-command CONFIG "CONFIG_{{ redis_admin_secret }}"
rename-command DEBUG ""

# Memory management
maxmemory 256mb
maxmemory-policy noeviction

# Performance
maxclients 1000
tcp-backlog 511
timeout 300
tcp-keepalive 300

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128