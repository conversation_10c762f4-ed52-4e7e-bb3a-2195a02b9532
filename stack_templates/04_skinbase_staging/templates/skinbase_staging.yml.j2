services:
  timescaledb:
    image: timescale/timescaledb-ha:pg17
    user: postgres
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
    secrets:
      - source: timescaledb_stage_password
        target: timescaledb_stage_password
    environment:
      - POSTGRES_USER=skinbase
      - POSTGRES_PASSWORD_FILE=/run/secrets/timescaledb_stage_password
      - POSTGRES_DB=skinbase
    healthcheck:
      test: ["CMD", "pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public

        - traefik.tcp.routers.timescaledb-stage.rule=HostSNI(`timescaledb-stage.{{ skinbase_base_domain }}`)
        - traefik.tcp.routers.timescaledb-stage.entrypoints=postgres
        - traefik.tcp.routers.timescaledb-stage.tls.certresolver=cloudflare
        - traefik.tcp.routers.timescaledb-stage.tls.options=postgres@file

        - traefik.tcp.services.timescaledb-stage.loadbalancer.server.port=5432
      placement:
        constraints:
          - node.hostname == netcup-manager-1
    networks:
      - default
      - traefik-public

  postgres:
    image: postgres:17-alpine
    user: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    secrets:
      - source: postgres_stage_password
        target: postgres_stage_password
    environment:
      - POSTGRES_USER=skinbase
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_stage_password
      - POSTGRES_DB=skinbase
    healthcheck:
      test: ["CMD", "pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public

        - traefik.tcp.routers.postgres-stage.rule=HostSNI(`postgres-stage.{{ skinbase_base_domain }}`)
        - traefik.tcp.routers.postgres-stage.entrypoints=postgres
        - traefik.tcp.routers.postgres-stage.tls.certresolver=cloudflare
        - traefik.tcp.routers.postgres-stage.tls.options=postgres@file

        - traefik.tcp.services.postgres-stage.loadbalancer.server.port=5432
      placement:
        constraints:
          - node.hostname == netcup-manager-1
    networks:
      - default
      - traefik-public

  redis-server:
    image: redis:7-alpine
    command: redis-server /usr/local/etc/redis/redis.conf
    secrets:
      - source: redis_stage_password
        target: redis_stage_password
      - source: redis_admin_secret
        target: redis_admin_secret
    configs:
      - source: redis_config
        target: /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "sh", "-c", "redis-cli -a \"$$(cat /run/secrets/redis_stage_password)\" ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - mode: host
        protocol: tcp
        published: 6379
        target: 6379
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == netcup-manager-1
    volumes:
      - redis_data:/data
    networks:
      - default
      - traefik-public

  external-api:
    image: ghcr.io/skinbase-io/external-api
    secrets:
      - source: timescaledb_stage_url
        target: DATABASE_URL
      - source: postgres_stage_url
        target: DATABASE_INTERNAL_URL
      - source: axiom_token
        target: axiom_token
    environment:
      - NODE_ENV=production
      - PORT=8080
      - LOG_LEVEL=info
      - REDIS_URL=redis://skinbase:{{ redis_stage_password }}@redis-server:6379
      - AXIOM_DATASET=internal-api
      - RATE_LIMIT_WINDOW_MS=10000
      - RATE_LIMIT_LIMIT=6

    stop_grace_period: 1m
    deploy:
      replicas: 1
      mode: replicated
      placement:
        constraints:
          - node.hostname == netcup-manager-1
        preferences:
          - spread: node.id
      update_config:
        order: start-first
        delay: 0s
        parallelism: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public

        - traefik.http.services.external-api-stage.loadbalancer.server.port=8080
        - traefik.http.routers.external-api-stage.rule=Host(`external-api-stage.{{ skinbase_base_domain }}`)
        - traefik.http.routers.external-api-stage.entrypoints=websecure
        - traefik.http.routers.external-api-stage.tls.certresolver=cloudflare

    networks:
      - default
      - traefik-public

  internal-api:
    image: ghcr.io/skinbase-io/internal-api:latest
    secrets:
      - source: timescaledb_stage_url
        target: DATABASE_URL
      - source: axiom_token
        target: axiom_token
    environment:
      - NODE_ENV=production
      - PORT=8081
      - LOG_LEVEL=info
      - REDIS_URL=redis://skinbase:{{ redis_stage_password }}@redis-server:6379
      - AXIOM_DATASET=web-scraping-worker
      - OTEL_SERVICE_NAME=internal-api
      - OTEL_EXPORTER_OTLP_ENDPOINT=https://api.axiom.co/v1/traces
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=https://api.axiom.co/v1/logs

      - MARKETCSGO_DELAY=15000
      - MARKETCSGO_BUY_DELAY=15000
      - SKINPORT_DELAY=15000
      - PIRATESWAP_DELAY=15000
      - BITSKINS_DELAY=15000
      - CSMONEY_DELAY=15000
      - BUFF_163_DELAY=15000
      - DMARKET_DELAY=15000

      - BUFF163_NUMBER_OF_CHUNKS=1
      - DMARKET_NUMBER_OF_CHUNKS=1
    stop_grace_period: 1m
    deploy:
      replicas: 1
      mode: replicated
      placement:
        constraints:
          - node.hostname == netcup-manager-1
        preferences:
          - spread: node.id
      update_config:
        order: start-first
        delay: 0s
        parallelism: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public

        - traefik.http.services.internal-api-stage.loadbalancer.server.port=8081
        - traefik.http.routers.internal-api-stage.rule=Host(`internal-api-stage.{{ skinbase_base_domain }}`)
        - traefik.http.routers.internal-api-stage.entrypoints=websecure
        - traefik.http.routers.internal-api-stage.tls.certresolver=cloudflare

    networks:
      - default
      - traefik-public

  web-scraping-worker:
    image: ghcr.io/skinbase-io/web-scraping-worker:latest
    secrets:
      - source: timescaledb_stage_url
        target: DATABASE_URL
      - source: axiom_token
        target: axiom_token
      - source: proxy_scrape_api_key
        target: PROXY_PROVIDER_API_KEY
    environment:
      - NODE_ENV=production
      - PORT=8082
      - LOG_LEVEL=info
      - REDIS_URL=redis://skinbase:{{ redis_stage_password }}@redis-server:6379
      - AXIOM_DATASET=web-scraping-worker
      - OTEL_SERVICE_NAME=web-scraping-worker
      - OTEL_EXPORTER_OTLP_ENDPOINT=https://api.axiom.co/v1/traces
      - OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=https://api.axiom.co/v1/logs

      - WORKER_CONCURRENCY=2
      - PROXY_PROVIDER=proxyscrape

      - SKINPORT_TIMEOUT=5000

      - PRICEMPIRE_CONCURRENCY=100
      - PRICEMPIRE_MAX_ATTEMPTS=25
      - PRICEMPIRE_TIMEOUT=5000

      - DMARKET_TIMEOUT=5000
      - DMARKET_MAX_ATTEMPTS=25
      - DMARKET_CONCURRENCY=300
      - DMARKET_LOG_CHUNK_SIZE=1000

    stop_grace_period: 3m
    deploy:
      replicas: 1
      mode: replicated
      placement:
        constraints:
          - node.hostname == netcup-manager-1
        preferences:
          - spread: node.id
      update_config:
        order: start-first
        delay: 0s
        parallelism: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
    networks:
      - default
      - traefik-public


  web:
    image: ghcr.io/skinbase-io/web
    secrets:
      - source: timescaledb_stage_url
        target: DATABASE_URL
      - source: postgres_stage_url
        target: DATABASE_INTERNAL_URL
      - source: nextauth_secret
        target: nextauth_secret
      - source: axiom_token
        target: axiom_token
      - source: steam_secret
        target: steam_secret
      - source: oxapay_api_key
        target: oxapay_api_key
    environment:
      - NODE_ENV=production
      - PORT=3333
      - HOSTNAME=0.0.0.0
      - LOG_LEVEL=info
      - REDIS_URL=redis://skinbase:{{ redis_stage_password }}@redis-server:6379
      - AXIOM_DATASET=internal-api
      - NEXTAUTH_URL=https://web-stage.{{ skinbase_base_domain }}
      - SKINSMONEY_SERVICE_ID=ef403863-9e99-45d5-8556-a95705123063
      - CDN_URL=https://cdn.{{ skinbase_base_domain }}
      - PAYMENT_SANDBOX_MODE=true

    stop_grace_period: 1m
    deploy:
      replicas: 1
      mode: replicated
      placement:
        constraints:
          - node.hostname == netcup-manager-1
        preferences:
          - spread: node.id
      update_config:
        order: start-first
        delay: 0s
        parallelism: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 256M
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public

        - traefik.http.services.web-stage.loadbalancer.server.port=3333
        - traefik.http.routers.web-stage.rule=Host(`web-stage.{{ skinbase_base_domain }}`)
        - traefik.http.routers.web-stage.entrypoints=websecure
        - traefik.http.routers.web-stage.tls.certresolver=cloudflare

    networks:
      - default
      - traefik-public

volumes:
  timescaledb_data:
  postgres_data:
  redis_data:

secrets:
  timescaledb_stage_password:
    file: ./secrets/timescaledb_stage_password
  timescaledb_stage_url:
    file: ./secrets/timescaledb_stage_url
  postgres_stage_password:
    file: ./secrets/postgres_stage_password
  postgres_stage_url:
    file: ./secrets/postgres_stage_url
  axiom_token:
    file: ./secrets/axiom_token
  nextauth_secret:
    file: ./secrets/nextauth_secret
  steam_secret:
    file: ./secrets/steam_secret
  oxapay_api_key:
    file: ./secrets/oxapay_api_key
  redis_stage_password:
    file: ./secrets/redis_stage_password
  redis_admin_secret:
    file: ./secrets/redis_admin_secret
  proxy_scrape_api_key:
    file: ./secrets/proxy_scrape_api_key

configs:
  redis_config:
    file: ./configs/redis.conf

networks:
  default:
    driver: overlay
    attachable: true
  traefik-public:
    external: true
